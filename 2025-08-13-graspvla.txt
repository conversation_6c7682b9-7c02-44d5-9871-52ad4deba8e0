╭───────────────────────────────────────────────────╮
│ ✻ Welcome to Claude Code!                         │
│                                                   │
│   /help for help, /status for your current setup  │
│                                                   │
│   cwd: /home/<USER>/workspace/develop/GraspVLA  │
│                                                   │
│   ─────────────────────────────────────────────── │
│                                                   │
│   Overrides (via env):                            │
│                                                   │
│   • API Base URL:                                 │
│   https://freeonlyinlinuxdo.alencryenfo.work      │
╰───────────────────────────────────────────────────╯

 Tips for getting started:

 1. Run /init to create a CLAUDE.md file with instructions for Claude
 2. Use <PERSON> to help with file analysis, editing, bash commands and git
 3. Be as specific as you would with another engineer for the best results

> 请详细分析并介绍 GraspVLA 项目的以下方面：

  1. **项目概述**：
     - 项目的主要目的和功能
     - 核心技术栈和架构
     - 项目的应用场景和目标用户

  2. **代码结构分析**：
     - 主要目录和文件的组织结构
     - 核心模块和组件的功能
     - 关键类和方法的作用

  3. **技术实现**：
     - 使用的主要框架和库
     - 核心算法和实现逻辑
     - 数据流程和处理方式

  4. **配置和依赖**：
     - 项目依赖的外部库和工具
     - 配置文件的作用和设置
     - 环境要求和安装步骤

  5. **功能特性**：
     - 主要功能模块的详细说明
     - 输入输出格式和接口
     - 性能特点和限制

  请基于实际的代码文件和项目结构进行分析，提供具体的代码示例和文件路径来支持你的介绍。如果某
  些方面的信息不够充分，请明确指出需要进一步调查的部分。

● 我将详细分析 GraspVLA 项目的各个方面。首先让我创建一个todo来系统地分析这个项目。

● Update Todos
  ⎿  ☐ 分析项目概述 - 目的、功能、技术栈  
     ☐ 分析代码结构 - 目录组织、核心模块
     ☐ 分析技术实现 - 框架、算法、数据流程
     ☐ 分析配置和依赖 - 外部库、配置文件
     ☐ 分析功能特性 - 模块功能、接口、性能

● 现在开始分析 GraspVLA 项目。让我先查看项目的整体结构。

● Update Todos
  ⎿  ☐ 分析项目概述 - 目的、功能、技术栈  
     ☐ 分析代码结构 - 目录组织、核心模块
     ☐ 分析技术实现 - 框架、算法、数据流程
     ☐ 分析配置和依赖 - 外部库、配置文件
     ☐ 分析功能特性 - 模块功能、接口、性能

● List(~/workspace/develop/GraspVLA)
  ⎿  Listed 44 paths (ctrl+r to expand)

● Read(README.md)
  ⎿  Read 120 lines (ctrl+r to expand)

● Read(requirements.txt)
  ⎿  Read 15 lines (ctrl+r to expand)

● Read(setup.py)
  ⎿  Read 12 lines (ctrl+r to expand)

● Read(vla_network/scripts/serve.py)
  ⎿  Read 50 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析项目概述 - 目的、功能、技术栈  
     ☐ 分析代码结构 - 目录组织、核心模块
     ☐ 分析技术实现 - 框架、算法、数据流程
     ☐ 分析配置和依赖 - 外部库、配置文件
     ☐ 分析功能特性 - 模块功能、接口、性能

● Read(vla_network/model/vla/__init__.py)
  ⎿  Read 429 lines (ctrl+r to expand)

● Read(vla_network/config/define.py)
  ⎿  Read 175 lines (ctrl+r to expand)

● Read(vla_network/type/data_type.py)
  ⎿  Read 76 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析项目概述 - 目的、功能、技术栈  
     ☒ 分析代码结构 - 目录组织、核心模块
     ☐ 分析技术实现 - 框架、算法、数据流程
     ☐ 分析配置和依赖 - 外部库、配置文件
     ☐ 分析功能特性 - 模块功能、接口、性能

● Read(vla_network/model/vla/flow_matching.py)
  ⎿  Read 100 lines (ctrl+r to expand)

● Read(vla_network/data_preprocessing/prompt.py)
  ⎿  Read 2 lines (ctrl+r to expand)

● Read(vla_network/model/backbone_2d/dinosiglip_vit.py)
  ⎿  Read 50 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析项目概述 - 目的、功能、技术栈  
     ☒ 分析代码结构 - 目录组织、核心模块
     ☒ 分析技术实现 - 框架、算法、数据流程
     ☐ 分析配置和依赖 - 外部库、配置文件
     ☐ 分析功能特性 - 模块功能、接口、性能

● Read(vla_network/utils/constant.py)
  ⎿  Read 1 line (ctrl+r to expand)

● Read(vla_network/data_preprocessing/preprocess.py)
  ⎿  Read 100 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析项目概述 - 目的、功能、技术栈  
     ☒ 分析代码结构 - 目录组织、核心模块
     ☒ 分析技术实现 - 框架、算法、数据流程
     ☒ 分析配置和依赖 - 外部库、配置文件
     ☐ 分析功能特性 - 模块功能、接口、性能

● Read(vla_network/scripts/offline_test.py)
  ⎿  Read 100 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析项目概述 - 目的、功能、技术栈  
     ☒ 分析代码结构 - 目录组织、核心模块
     ☒ 分析技术实现 - 框架、算法、数据流程
     ☒ 分析配置和依赖 - 外部库、配置文件
     ☒ 分析功能特性 - 模块功能、接口、性能

● (no content)
  基于对 GraspVLA 项目代码的详细分析，我将从以下五个方面进行完整介绍：

  1. 项目概述

  项目目的和功能

  GraspVLA 是一个专注于机器人抓取任务的视觉-语言-动作（Vision-Language-Action）基础模型，主要
  特点包括：

  - SynGrasp-1B 数据集：包含十亿帧合成抓取数据，涵盖240个物体类别和10,000+个物体
  - 零样本泛化能力：在合成数据上预训练后可直接迁移到真实世界抓取任务
  - 统一CoT框架：集成自回归感知和基于流匹配的动作生成，支持开放词汇抓取

  核心技术栈

  - 深度学习框架：PyTorch 2.7.1, Transformers 4.53.1
  - 视觉模型：DinoSigLIP ViT (224x384分辨率)
  - 语言模型：InternLM2-1.8B 或 Llama2-7B
  - 通信协议：ZMQ (ZeroMQ) 用于模型服务器通信
  - 图像处理：OpenCV, PIL, torchvision

  应用场景

  - 机器人抓取任务规划
  - 真实世界物体操作
  - 仿真环境评估（LIBERO基准）
  - 开放词汇抓取指令理解

  2. 代码结构分析

  主要目录组织

  GraspVLA/
  ├── vla_network/           # 核心模型代码
  │   ├── config/           # 配置定义
  │   ├── data_preprocessing/ # 数据预处理
  │   ├── model/           # 模型架构
  │   │   ├── backbone_2d/ # 2D视觉骨干网络
  │   │   ├── backbone_llm/ # LLM骨干网络
  │   │   └── vla/         # VLA核心模块
  │   ├── scripts/         # 服务脚本
  │   ├── type/           # 数据类型定义
  │   └── utils/          # 工具函数
  ├── visualization/      # 可视化结果
  └── figs/              # 项目图片

  核心模块功能

  VLA模型核心 (vla_network/model/vla/__init__.py:57)

  - VLA类：主要的模型类，集成视觉、语言和动作预测
  - VLAAgent类：模型代理，提供推理接口
  - 关键方法：
    - generate()：生成抓取动作和CoT推理
    - generate_flow_matching()：流匹配动作生成
    - generate_autoregressive()：自回归生成bbox和目标tokens

  视觉骨干网络 (vla_network/model/backbone_2d/dinosiglip_vit.py:50)

  - DinoSigLIPViTBackbone：结合DinoV2和SigLIP的双视觉编码器
  - 支持的分辨率：224x224 和 384x384
  - 特征提取：使用ViT的中间层输出

  数据预处理 (vla_network/data_preprocessing/preprocess.py:52)

  - DataPreprocessor类：统一的数据预处理管道
  - 图像处理：resize、padding、bbox变换
  - 数据标准化：动作和本体感觉数据的归一化

  3. 技术实现

  核心算法架构

  统一CoT框架

  # vla_network/model/vla/__init__.py:208
  if self.config.pred == "cot_flow_matching":
      # 生成bbox和goal tokens的自回归过程
      cot_parse, kv_cache = self.generate_autoregressive(...)

      # 使用流匹配生成动作
      action = self.generate_flow_matching(
          prefix_kv_cache=kv_cache,
          prefix_mask=prefix_mask,
          ...
      )

  流匹配动作生成 (vla_network/model/vla/flow_matching.py:59)

  - VLAFlowMatchingModule：基于流的动作生成模块
  - 时间嵌入：使用正弦余弦位置编码
  - 去噪过程：迭代优化动作序列
  - 关键公式：x_t = t * noise + (1 - t) * x_1

  数据流程

  输入处理流程

  1. 图像输入：前视图和侧视图 (256x256x3)
  2. 本体感觉：7维状态向量 (xyz, rpy, gripper)
  3. 文本指令：自然语言抓取指令
  4. 预处理：resize到224x224，标准化

  输出格式

  - 动作序列：7维动作 (xyz位移, rpy旋转, gripper状态)
  - 边界框：2D检测框用于目标定位
  - 3D目标：6维目标位姿 (xyz, rpy)

  关键实现细节

  块注意力机制 (vla_network/model/vla/__init__.py:37)

  def make_block_attn_mask(input_mask, block_mask):
      # 创建因果+块的注意力掩码
      causal_mask = torch.tril(torch.ones(...))
      if causal_num != len(block_mask):
          block_attn_mask = cumsum[None, causal_num:] <= cumsum[causal_num:, None]
          causal_mask[causal_num:, causal_num:] = block_attn_mask

  动作专家网络 (vla_network/model/vla/__init__.py:100)

  - 从LLM派生的专门动作预测网络
  - 缩小的隐藏层维度以提高效率
  - 支持flex_attention实现

  4. 配置和依赖

  主要依赖库

  - PyTorch生态系统：torch, torchvision, transformers
  - 计算机视觉：opencv-python, Pillow, timm
  - 数据处理：numpy, transforms3d, safetensors
  - 通信：pyzmq (ZeroMQ)
  - 配置管理：pydantic

  配置文件结构 (vla_network/config/define.py)

  模型配置 (VLAModelConfig)

  class VLAModelConfig:
      backbone_2d: Backbone2DConfig    # 视觉骨干配置
      llm: LLMConfig                   # 语言模型配置
      pred: str                       # 预测类型 ("cot_flow_matching")
      action_expert: bool             # 是否使用动作专家
      flow_matching_cfg: FlowMatchingConfig  # 流匹配配置

  数据配置 (VLADataConfig)

  class VLADataConfig:
      robot: str                      # 机器人类型
      proprio_len: int               # 本体感觉序列长度
      action_len: int                # 动作序列长度
      action_dim: int                # 动作维度 (7)
      img_key: List[str]            # 图像键名 ["front", "side"]
      image_size: int               # 图像尺寸 (224)

  环境要求

  - Python 3.8+
  - CUDA支持
  - ~9GB GPU内存 (RTX L40s)
  - 200ms推理延迟

  5. 功能特性

  主要功能模块

  模型服务器 (vla_network/scripts/serve.py)

  - ZMQ服务器：处理推理请求
  - 批处理支持：支持多样本并行推理
  - 编译优化：可选torch编译提升性能
  - 动作插值：interpolate_delta_actions()平滑动作序列

  离线测试 (vla_network/scripts/offline_test.py)

  - 验证服务：检查模型服务器状态
  - 可视化：生成边界框和抓取结果对比
  - 性能测试：预录制数据的推理验证

  数据类型系统 (vla_network/type/data_type.py)

  - RawVLAData：原始输入数据格式
  - BatchVLAData：批处理数据格式
  - 类型安全：Pydantic验证确保数据一致性

  输入输出接口

  输入格式

  {
      'front_view_image': [np.ndarray],  # 前视图图像序列
      'side_view_image': [np.ndarray],   # 侧视图图像序列
      'proprio_array': [np.ndarray],     # 本体感觉状态序列
      'text': str,                       # 自然语言指令
      'env_id': str                      # 环境标识符
  }

  输出格式

  {
      'action': np.ndarray,              # 7维动作序列
      'goal': (np.ndarray, np.ndarray),  # 3D目标位置和姿态
      'bbox': np.ndarray                 # 2D边界框坐标
  }

  性能特点

  推理性能

  - 延迟：200ms (编译后) / 500ms (未编译)
  - 内存：~9GB GPU内存
  - 启动时间：编译增加约3分钟启动时间

  泛化能力

  - 零样本迁移：合成数据预训练，真实世界直接应用
  - 开放词汇：支持任意自然语言抓取指令
  - 多视角：前视图和侧视图融合

  限制

  - 单样本推理：当前仅支持batch_size=1
  - 固定动作长度：预定义的动作序列长度
  - 依赖预训练：需要大规模合成数据预训练

  这个项目代表了机器人抓取领域的重要进展，通过合成数据预训练和统一的CoT框架，实现了高效的零样
  本泛化能力。

> 请详细分析VLA（Vision-Language-Action）模型的推理pipeline，包括以下具体内容：

  1. **推理流程分析**：
     - 从输入到输出的完整数据流程
     - 每个处理阶段的具体功能和作用
     - 各个组件之间的交互关系

  2. **数据维度变化追踪**：
     - 输入数据的初始维度（图像、文本、状态等）
     - 每个处理步骤中数据张量的维度变化
     - 中间特征表示的维度信息
     - 最终输出动作的维度格式

  3. **可视化展示**：
     - 使用Mermaid流程图清晰展示整个推理pipeline
     - 在流程图中标注关键的数据维度信息
     - 突出显示重要的数据转换节点

  请基于当前代码库中的实际实现进行分析，特别关注`vla_network/scripts/offline_test.py`文件及其
  相关的推理代码，确保分析的准确性和完整性。如果需要查看其他相关文件来完整理解推理流程，请一
  并分析。

● 我将详细分析VLA模型的推理pipeline。让我创建一个todo来系统地分析这个流程。

● Update Todos
  ⎿  ☐ 分析推理流程 - 完整数据流程和组件交互  
     ☐ 追踪数据维度变化 - 输入到输出的维度变换
     ☐ 创建Mermaid流程图 - 可视化推理pipeline

● Update Todos
  ⎿  ☐ 分析推理流程 - 完整数据流程和组件交互  
     ☐ 追踪数据维度变化 - 输入到输出的维度变换
     ☐ 创建Mermaid流程图 - 可视化推理pipeline

● (no content)
  让我开始详细分析VLA模型的推理pipeline。首先深入查看推理相关的核心代码。

● Read(vla_network/scripts/offline_test.py)
  ⎿  Read 156 lines (ctrl+r to expand)

● Read(vla_network/scripts/serve.py)
  ⎿  Read 150 lines (ctrl+r to expand)

● 现在让我查看模型推理的核心代码部分：

● Read(vla_network/scripts/serve.py)
  ⎿  Read 21 lines (ctrl+r to expand)

● Read(vla_network/model/vla/__init__.py)
  ⎿  Read 50 lines (ctrl+r to expand)

● 让我查看数据预处理和tokenization的详细实现：

● Read(vla_network/data_preprocessing/vla_data_collator.py)
  ⎿  Read 36 lines (ctrl+r to expand)

● Read(vla_network/data_preprocessing/token_pattern.py)
  ⎿  Read 80 lines (ctrl+r to expand)

● 现在让我查看视觉特征提取和模型生成的核心部分：

● Read(vla_network/model/vla/__init__.py)
  ⎿  Read 50 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析推理流程 - 完整数据流程和组件交互  
     ☐ 追踪数据维度变化 - 输入到输出的维度变换
     ☐ 创建Mermaid流程图 - 可视化推理pipeline

● (no content)
  现在让我检查视觉骨干网络的输出维度：

● Read(vla_network/model/backbone_2d/__init__.py)
  ⎿  Read 32 lines (ctrl+r to expand)

● Read(vla_network/model/backbone_2d/dinosiglip_vit.py)
  ⎿  Read 50 lines (ctrl+r to expand)

● 让我查看完整的forward方法：

● Read(vla_network/model/backbone_2d/dinosiglip_vit.py)
  ⎿  Read 17 lines (ctrl+r to expand)

● Update Todos
  ⎿  ☒ 分析推理流程 - 完整数据流程和组件交互  
     ☒ 追踪数据维度变化 - 输入到输出的维度变换
     ☐ 创建Mermaid流程图 - 可视化推理pipeline

● (no content)
  现在基于详细的代码分析，我将提供完整的VLA模型推理pipeline分析：

  1. 推理流程分析

  完整数据流程

  1.1 输入接收阶段 (serve.py:49-67)

  def infer_single_sample(vla_model: VLAAgent, sample: dict):
      # 输入数据格式：
      # {
      #     'front_view_image': [np.ndarray],  # (256, 256, 3)
      #     'side_view_image': [np.ndarray],   # (256, 256, 3) 
      #     'proprio_array': [np.ndarray]*4,  # (7,) 每个元素
      #     'text': str                        # 自然语言指令
      # }

  1.2 数据预处理阶段 (serve.py:58-66)

  - 图像选择：只使用最后一帧图像 (sample['front_view_image'][-1])
  - 本体感觉处理：选择第1帧和最后一帧的状态，归一化gripper状态
  - CoT提示：使用COT_PROMPT(sample['text'])生成结构化提示

  1.3 原始数据封装 (vla/__init__.py:413-428)

  raw = RawVLAData(
      instruction=sample['text'],           # 自然语言指令
      images=dict(
          front=np.stack(sample['front_view_image']),  # (1, 256, 256, 3)
          side=np.stack(sample['side_view_image']),    # (1, 256, 256, 3)
      ),
      proprio=np.stack(sample['proprio_array']),      # (4, 7)
  )

  1.4 数据变换 (vla/__init__.py:387)

  x = self.preprocessor.transform(raw, inference=True)
  # 执行图像resize (256x256 -> 224x224)
  # 数据标准化和tokenization

  1.5 模型推理 (vla/__init__.py:389-399)

  token_result, action_result = self.model.generate(
      input_ids=model_input['input_ids'],      # 文本token IDs
      robot_input_ids=model_input['robot_input_ids'],  # 机器人token IDs
      attention_mask=model_input['attention_mask'],    # 注意力掩码
      images=model_input['images'],             # 预处理后的图像
      proprio=model_input['proprio'],           # 本体感觉数据
      inference_kwargs=x.inference_kwargs,      # 推理参数
      token_pattern=self.token_pattern,         # token模式
  )

  1.6 CoT推理阶段 (vla/__init__.py:208-220)

  - 自回归生成：生成边界框和目标tokens
  - KV缓存：保存中间结果用于流匹配

  1.7 流匹配动作生成 (vla/__init__.py:238-245)

  - 噪声采样：生成初始噪声
  - 迭代去噪：10次迭代优化动作序列

  1.8 后处理 (serve.py:69-84)

  - 动作量化：将gripper状态量化为{-1, 0, 1}
  - 动作插值：将动作序列插值2倍 (interpolate_delta_actions)

  2. 数据维度变化追踪

  详细维度变化过程

  输入维度

  输入数据:
  ├── front_view_image: [1, 256, 256, 3]  # 前视图 (H, W, C)
  ├── side_view_image: [1, 256, 256, 3]   # 侧视图 (H, W, C)
  ├── proprio_array: [4, 7]                # 本体感觉状态序列
  └── text: str                            # 自然语言指令

  图像预处理维度变化

  原始图像: [1, 256, 256, 3]
      ↓ resize (256→224)
  Resize图像: [1, 224, 224, 3]
      ↓ 图像变换 (归一化等)
  预处理图像: [1, 224, 224, 3]
      ↓ 批处理 + 通道维度调整
  模型输入图像: [1, 2, 3, 224, 224]  # [batch, num_views, channels, height, width]

  视觉特征提取维度变化 (dinosiglip_vit.py:96-102)

  输入图像: [1, 2, 3, 224, 224]
      ↓ 分别处理Dino和SigLIP
  Dino特征: [1, 2, 256, 1024]      # 1024 = embed_dim
  SigLIP特征: [1, 2, 256, 1152]    # 1152 = embed_dim
      ↓ 沿通道维度拼接
  拼接特征: [1, 2, 256, 2176]      # 2176 = 1024 + 1152
      ↓ 重塑维度
  视觉特征: [1, 512, 2176]         # 512 = 2 * 256 (两个视图的token数)

  投影器维度变化

  视觉特征: [1, 512, 2176]          # 2176 = feature_dim
      ↓ 线性投影
  投影特征: [1, 512, 2048]          # 2048 = llm_dim (InternLM2-1.8B)

  文本处理维度变化

  文本指令: "In: What action should the robot take to pick up the cup?\nOut: "
      ↓ Tokenization
  input_ids: [1, N_token]          # N_token ≈ 20-30
  attention_mask: [1, N_token]

  多模态融合维度变化 (vla/__init__.py:145-173)

  文本嵌入: [1, N_token, 2048]
  视觉特征: [1, 512, 2048]
      ↓ 插入视觉特征到文本序列
  多模态嵌入: [1, N_token+512, 2048]
  注意力掩码: [1, N_token+512]

  CoT自回归生成维度

  生成的tokens:
  ├── bbox tokens: [8]              # 4个bbox * 2个视图
  ├── goal tokens: [6]              # xyz + rpy
  └── 其他tokens: [10-20]           # 分隔符等
  总计: ~24-34 tokens

  流匹配动作生成维度 (flow_matching.py:59-100)

  本体感觉输入: [1, 7]              # 最后时刻的本体感觉状态
      ↓ 线性投影
  本体感觉嵌入: [1, 2048]           # 2048 = llm_dim

  噪声采样: [1, action_len, 7]     # action_len=6, 7=action_dim
      ↓ 迭代去噪 (10次)
  去噪动作: [1, 6, 7]              # 6步动作序列，7维动作

  最终输出维度

  模型输出:
  ├── action: [6, 7]               # 6步动作序列，每步7维 (xyz, rpy, gripper)
  ├── goal: ([3], [3])             # 目标位置(xyz)和姿态(rpy)
  └── bbox: [2, 4]                 # 前视图和侧视图的边界框

  后处理维度变化

  动作序列: [6, 7]
      ↓ 动作插值 (2倍)
  插值动作: [12, 7]                # 最终输出给机器人的动作序列

  3. 可视化展示

  VLA模型推理Pipeline流程图

  graph TD
      A[输入数据] --> B[数据预处理]
      B --> C[特征提取]
      C --> D[多模态融合]
      D --> E[CoT推理]
      E --> F[流匹配动作生成]
      F --> G[后处理]
      G --> H[最终输出]

      subgraph 输入数据
          A1[前视图图像<br/>1×256×256×3]
          A2[侧视图图像<br/>1×256×256×3]
          A3[本体感觉序列<br/>4×7]
          A4[文本指令<br/>字符串]
      end

      subgraph 数据预处理
          B1[图像选择<br/>选择最后一帧]
          B2[图像Resize<br/>256×256→224×224]
          B3[本体感觉处理<br/>选择帧1&4，归一化]
          B4[CoT提示生成<br/>结构化指令]
      end

      subgraph 特征提取
          C1[DinoViT特征<br/>1×2×256×1024]
          C2[SigLIPViT特征<br/>1×2×256×1152]
          C3[特征拼接<br/>1×2×256×2176]
          C4[特征投影<br/>1×512×2048]
      end

      subgraph 多模态融合
          D1[文本Tokenization<br/>1×N_token×2048]
          D2[视觉特征插入<br/>1×(N_token+512)×2048]
          D3[注意力掩码生成<br/>1×(N_token+512)]
      end

      subgraph CoT推理
          E1[自回归生成<br/>bbox tokens]
          E2[自回归生成<br/>goal tokens]
          E3[KV缓存保存<br/>用于流匹配]
      end

      subgraph 流匹配动作生成
          F1[噪声采样<br/>1×6×7]
          F2[本体感觉投影<br/>1×2048]
          F3[迭代去噪<br/>10次迭代]
          F4[动作输出<br/>1×6×7]
      end

      subgraph 后处理
          G1[动作量化<br/>gripper: {-1,0,1}]
          G2[动作插值<br/>6→12步]
          G3[目标解码<br/>xyz + rpy]
          G4[边界框解码<br/>2×4坐标]
      end

      subgraph 最终输出
          H1[动作序列<br/>12×7]
          H2[3D目标位姿<br/>位置+姿态]
          H3[2D边界框<br/>前视图+侧视图]
      end

      A --> A1
      A --> A2
      A --> A3
      A --> A4

      A1 --> B1
      A2 --> B1
      A3 --> B3
      A4 --> B4

      B1 --> B2
      B2 --> C1
      B2 --> C2
      B3 --> F2

      C1 --> C3
      C2 --> C3
      C3 --> C4
      C4 --> D2

      B4 --> D1
      D1 --> D2
      D2 --> E1

      E1 --> E2
      E2 --> E3
      E3 --> F3

      F2 --> F3
      F1 --> F3
      F3 --> F4
      F4 --> G1

      G1 --> G2
      G2 --> H1
      E2 --> G3
      G3 --> H2
      E1 --> G4
      G4 --> H3

      style 输入数据 fill:#e1f5fe
      style 数据预处理 fill:#f3e5f5
      style 特征提取 fill:#e8f5e8
      style 多模态融合 fill:#fff3e0
      style CoT推理 fill:#fce4ec
      style 流匹配动作生成 fill:#f1f8e9
      style 后处理 fill:#e0f2f1
      style 最终输出 fill:#efebe9

  关键数据维度总结表

  | 处理阶段  | 组件           | 输入维度                 | 输出维度                 | 说明
              |
  |-------|--------------|----------------------|----------------------|-----------------|
  | 输入    | 前视图图像        | 1×256×256×3          | 1×256×256×3          | RGB图像
       |
  | 输入    | 侧视图图像        | 1×256×256×3          | 1×256×256×3          | RGB图像
       |
  | 输入    | 本体感觉         | 4×7                  | 4×7                  |
  xyz+rpy+gripper |
  | 预处理   | 图像Resize     | 1×256×256×3          | 1×224×224×3          | 统一尺寸
       |
  | 预处理   | 图像变换         | 1×224×224×3          | 1×224×224×3          | 归一化等
         |
  | 特征提取  | DinoViT      | 1×3×224×224          | 1×256×1024           | 视觉特征
      |
  | 特征提取  | SigLIPViT    | 1×3×224×224          | 1×256×1152           | 视觉特征
      |
  | 特征融合  | 特征拼接         | [256×1024, 256×1152] | 512×2176             | 多视图特征
           |
  | 投影    | 线性投影         | 512×2176             | 512×2048             | 对齐LLM维度
        |
  | 文本处理  | Tokenization | 字符串                  | 1×N_token            | 文本tokens
       |
  | 多模态   | 特征插入         | [文本+视觉]              | 1×(N_token+512)×2048 |
  多模态嵌入           |
  | CoT推理 | 自回归生成        | 上下文                  | ~24-34 tokens        | bbox+goal
        |
  | 流匹配   | 噪声采样         | -                    | 1×6×7                | 初始噪声
         |
  | 流匹配   | 迭代去噪         | 噪声+上下文               | 1×6×7                | 优化动作
              |
  | 后处理   | 动作插值         | 6×7                  | 12×7                 | 平滑动作
         |
  | 输出    | 最终动作         | -                    | 12×7                 | 机器人执行序列
           |

  这个完整的分析展示了VLA模型如何从多模态输入（图像、文本、状态）通过复杂的推理pipeline生成最
  终的机器人抓取动作序列。整个流程涉及了视觉特征提取、多模态融合、CoT推理和流匹配等多个关键技
  术环节。

